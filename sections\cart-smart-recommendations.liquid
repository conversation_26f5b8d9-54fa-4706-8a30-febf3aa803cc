{%- liquid
  assign per_row = section.settings.per_row
  assign product_limit = per_row | times: section.settings.rows
  
  # 获取购物车中的商品信息用于智能推荐
  assign cart_product_types = ''
  assign cart_product_tags = ''
  assign cart_product_vendors = ''
  assign cart_product_collections = ''
  
  for item in cart.items
    # 收集产品类型
    if item.product.product_type != blank
      unless cart_product_types contains item.product.product_type
        assign cart_product_types = cart_product_types | append: item.product.product_type | append: ','
      endunless
    endif

    # 收集产品标签
    for tag in item.product.tags
      if tag != blank
        unless cart_product_tags contains tag
          assign cart_product_tags = cart_product_tags | append: tag | append: ','
        endunless
      endif
    endfor

    # 收集产品品牌
    if item.product.vendor != blank
      unless cart_product_vendors contains item.product.vendor
        assign cart_product_vendors = cart_product_vendors | append: item.product.vendor | append: ','
      endunless
    endif

    # 收集产品系列（集合）
    for collection in item.product.collections
      if collection.handle != blank and collection.handle != 'all'
        unless cart_product_collections contains collection.handle
          assign cart_product_collections = cart_product_collections | append: collection.handle | append: ','
        endunless
      endif
    endfor
  endfor
  
  # 移除末尾的逗号
  if cart_product_types != blank
    assign cart_product_types = cart_product_types | remove_last: ','
  endif
  if cart_product_tags != blank
    assign cart_product_tags = cart_product_tags | remove_last: ','
  endif
  if cart_product_vendors != blank
    assign cart_product_vendors = cart_product_vendors | remove_last: ','
  endif
  if cart_product_collections != blank
    assign cart_product_collections = cart_product_collections | remove_last: ','
  endif
  
  # 智能推荐逻辑：收集相关商品
  assign recommended_count = 0

  # 如果购物车为空，使用默认推荐集合
  if cart.item_count == 0
    # 购物车为空时的处理在HTML部分
  else
    # 全新的简化推荐算法
    assign recommended_products_temp = ''
    assign recommended_count = 0

    # 检查购物车中是否有IR3 V2打印机
    assign has_ir3_printer = false
    for cart_item in cart.items
      assign cart_title_lower = cart_item.product.title | downcase
      if cart_title_lower contains 'ideaformer ir3 v2' and cart_title_lower contains 'printer'
        assign has_ir3_printer = true
        break
      endif
    endfor

    # 第一阶段：如果有IR3打印机，优先推荐IR3配件（随机化）
    if has_ir3_printer
      assign ir3_accessories = collections['ir3-v2-printer-accessories']
      if ir3_accessories and ir3_accessories.products_count > 0
        # 收集所有符合条件的IR3配件
        assign ir3_candidates = ''
        for product in ir3_accessories.products limit: 10
          assign skip_product = false
          assign product_title_lower = product.title | downcase

          # 跳过已在购物车中的商品
          for cart_item in cart.items
            if cart_item.product.id == product.id
              assign skip_product = true
              break
            endif
          endfor

          # 严格过滤：只推荐真正的IR3相关配件
          unless product_title_lower contains 'ideaformer' or product_title_lower contains 'ir3' or product_title_lower contains 'conveyor'
            assign skip_product = true
          endunless

          # 排除其他品牌专用配件
          if product_title_lower contains 'bambu' or product_title_lower contains 'p1p' or product_title_lower contains 'x1c'
            assign skip_product = true
          endif

          unless skip_product
            assign ir3_candidates = ir3_candidates | append: product.handle | append: ','
          endunless
        endfor

        # 从候选商品中随机选择2个
        if ir3_candidates != blank
          assign ir3_array = ir3_candidates | remove_last: ',' | split: ','
          assign current_time = 'now' | date: '%s'
          assign random_seed = current_time | modulo: ir3_array.size

          # 随机选择起始位置
          for i in (0..1)
            assign index = random_seed | plus: i | modulo: ir3_array.size
            assign selected_handle = ir3_array[index]
            if selected_handle != blank
              assign recommended_products_temp = recommended_products_temp | append: selected_handle | append: ','
              assign recommended_count = recommended_count | plus: 1
            endif
          endfor
        endif
      endif
    endif

    # 第二阶段：从购物车商品的集合中推荐剩余商品
    if recommended_count < product_limit and cart_product_collections != blank
      assign remaining_slots = product_limit | minus: recommended_count
      assign collection_handles = cart_product_collections | split: ','

      for collection_handle in collection_handles
        if remaining_slots <= 0
          break
        endif

        # 跳过已经处理过的IR3配件集合
        if collection_handle == 'ir3-v2-printer-accessories'
          continue
        endif

        assign target_collection = collections[collection_handle]
        if target_collection and target_collection.products_count > 0
          # 收集该集合的候选商品
          assign collection_candidates = ''
          for product in target_collection.products limit: 20
            assign skip_product = false

            # 跳过已在购物车中的商品
            for cart_item in cart.items
              if cart_item.product.id == product.id
                assign skip_product = true
                break
              endif
            endfor

            # 跳过已经推荐的商品
            if recommended_products_temp contains product.handle
              assign skip_product = true
            endif

            # 对所有推荐商品进行基本过滤
            assign product_title_lower = product.title | downcase
            if product_title_lower contains 'bambu' or product_title_lower contains 'p1p' or product_title_lower contains 'x1c'
              assign skip_product = true
            endif

            unless skip_product
              assign collection_candidates = collection_candidates | append: product.handle | append: ','
            endunless
          endfor

          # 从候选商品中随机选择
          if collection_candidates != blank
            assign candidates_array = collection_candidates | remove_last: ',' | split: ','
            assign current_time = 'now' | date: '%s'
            assign random_seed = current_time | modulo: candidates_array.size

            # 随机选择商品填充剩余位置
            for i in (0..remaining_slots)
              if remaining_slots <= 0
                break
              endif

              assign index = random_seed | plus: i | modulo: candidates_array.size
              assign selected_handle = candidates_array[index]
              if selected_handle != blank
                assign recommended_products_temp = recommended_products_temp | append: selected_handle | append: ','
                assign recommended_count = recommended_count | plus: 1
                assign remaining_slots = remaining_slots | minus: 1
              endif
            endfor
          endif
        endif
      endfor
    endif

    # 第三阶段：如果仍然不够，从通用集合随机补充
    if recommended_count < product_limit
      assign remaining_slots = product_limit | minus: recommended_count

      # 收集通用候选商品
      assign general_candidates = ''
      for product in collections.all.products limit: 100
        assign skip_product = false
        assign product_title_lower = product.title | downcase

        # 跳过已在购物车中的商品
        for cart_item in cart.items
          if cart_item.product.id == product.id
            assign skip_product = true
            break
          endif
        endfor

        # 跳过已经推荐的商品
        if recommended_products_temp contains product.handle
          assign skip_product = true
        endif

        # 基本过滤：排除其他品牌专用配件
        if product_title_lower contains 'bambu' or product_title_lower contains 'p1p' or product_title_lower contains 'x1c'
          assign skip_product = true
        endif

        # 基本相关性检查：至少包含3D打印相关关键词
        assign is_relevant = false
        assign keywords = 'printer,print,3d,pei,plate,build,nozzle,extruder,filament,bed,hotend,kit,parts,accessories' | split: ','
        for keyword in keywords
          if product_title_lower contains keyword
            assign is_relevant = true
            break
          endif
        endfor

        unless is_relevant
          assign skip_product = true
        endunless

        unless skip_product
          assign general_candidates = general_candidates | append: product.handle | append: ','
        endunless
      endfor

      # 从通用候选商品中随机选择
      if general_candidates != blank
        assign general_array = general_candidates | remove_last: ',' | split: ','
        assign current_time = 'now' | date: '%s'
        assign random_seed = current_time | modulo: general_array.size

        # 随机选择商品填充剩余位置
        for i in (0..remaining_slots)
          if remaining_slots <= 0
            break
          endif

          assign index = random_seed | plus: i | modulo: general_array.size
          assign selected_handle = general_array[index]
          if selected_handle != blank
            assign recommended_products_temp = recommended_products_temp | append: selected_handle | append: ','
            assign recommended_count = recommended_count | plus: 1
            assign remaining_slots = remaining_slots | minus: 1
          endif
        endfor
      endif
    endif
    
    # 如果没有找到足够的相关商品，使用默认集合补充
    if recommended_count < product_limit
      assign fallback_collection = collections[section.settings.fallback_collection]
      if fallback_collection and fallback_collection.products_count > 0
        for product in fallback_collection.products limit: product_limit
          assign skip_product = false
          for cart_item in cart.items
            if cart_item.product.id == product.id
              assign skip_product = true
              break
            endif
          endfor

          unless skip_product
            unless recommended_products_temp contains product.handle
              assign recommended_products_temp = recommended_products_temp | append: product.handle | append: ','
              assign recommended_count = recommended_count | plus: 1
            endunless
          endunless

          if recommended_count >= product_limit
            break
          endif
        endfor
      endif
    endif

    # 处理推荐商品列表
    if recommended_products_temp != blank
      assign recommended_products_temp = recommended_products_temp | remove_last: ','
      assign recommended_handles_array = recommended_products_temp | split: ','
    endif
  endif
  

-%}

<style>
  .cart-smart-recommendations {
    background: #fafafa;
    padding: 60px 0;
    margin-top: 40px;
  }
  
  .cart-smart-recommendations .section-header {
    margin-bottom: 40px;
  }
  
  .cart-smart-recommendations .section-header__title {
    font-family: 'Helvetica Neue', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 2.4em;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 12px;
    letter-spacing: -0.02em;
    line-height: 1.2;
  }

  .cart-smart-recommendations .section-header__subtitle {
    font-family: 'Helvetica Neue', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 1.15em;
    color: #666;
    font-weight: 400;
    letter-spacing: 0.01em;
  }
  
  .recommendations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-top: 40px;
  }
  
  @media (max-width: 768px) {
    .cart-smart-recommendations {
      padding: 40px 0;
    }
    
    .recommendations-grid {
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
    }
    
    .cart-smart-recommendations .section-header__title {
      font-size: 1.8em;
    }
  }
  
  @media (max-width: 480px) {
    .recommendations-grid {
      grid-template-columns: 1fr;
    }
  }
  
  .recommendation-item {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    position: relative;
  }
  
  .recommendation-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
  }
  
  .recommendation-item__image {
    position: relative;
    overflow: hidden;
    aspect-ratio: 1;
  }
  
  .recommendation-item__image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }
  
  .recommendation-item:hover .recommendation-item__image img {
    transform: scale(1.05);
  }
  
  .recommendation-item__content {
    padding: 20px;
  }
  
  .recommendation-item__title {
    font-size: 1.1em;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    line-height: 1.3;
  }
  
  .recommendation-item__title a {
    color: inherit;
    text-decoration: none;
  }
  
  .recommendation-item__title a:hover {
    color: var(--colorAccent, #007bff);
  }
  
  .recommendation-item__price {
    font-size: 1.2em;
    font-weight: 700;
    color: var(--colorAccent, #007bff);
    margin-bottom: 15px;
  }
  
  .recommendation-item__price .compare-price {
    font-size: 0.9em;
    color: #999;
    text-decoration: line-through;
    margin-right: 8px;
    font-weight: 400;
  }

  .recommendation-item__actions {
    margin-top: 15px;
  }

  .add-to-cart-btn {
    width: 100%;
    background: var(--colorAccent, #007bff);
    color: white;
    border: none;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 0.95em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  .add-to-cart-btn:hover {
    background: var(--colorAccentDark, #0056b3);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,123,255,0.3);
  }

  .add-to-cart-btn:active {
    transform: translateY(0);
  }

  .add-to-cart-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }

  .add-to-cart-btn .btn-icon {
    font-size: 1.1em;
    font-weight: 600;
  }

  .add-to-cart-btn .btn-icon::before {
    content: '+';
    display: inline-block;
    margin-right: 2px;
    font-size: 1.2em;
    font-weight: 700;
  }
  
  .recommendation-item__badge {
    position: absolute;
    top: 12px;
    left: 12px;
    background: var(--colorAccent, #007bff);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8em;
    font-weight: 600;
    text-transform: uppercase;
  }
  
  .recommendation-item__badge--sale {
    background: #e74c3c;
  }
  
  .empty-recommendations {
    text-align: center;
    padding: 60px 20px;
    color: #666;
  }
  
  .empty-recommendations__icon {
    font-size: 3em;
    margin-bottom: 20px;
    opacity: 0.5;
  }
  
  .empty-recommendations__text {
    font-size: 1.1em;
    margin-bottom: 20px;
  }
  
  .empty-recommendations__link {
    display: inline-block;
    padding: 12px 24px;
    background: var(--colorAccent, #007bff);
    color: white;
    text-decoration: none;
    border-radius: 6px;
    font-weight: 600;
    transition: background-color 0.3s ease;
  }
  
  .empty-recommendations__link:hover {
    background: var(--colorAccentDark, #0056b3);
    color: white;
  }

  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
</style>

{% if cart.item_count > 0 %}
<div class="cart-smart-recommendations">
  <div class="page-width">
    <div class="section-header text-center">
      <h2 class="section-header__title">
        {{ section.settings.title | default: "You May Also Like" }}
      </h2>
    </div>

    {% if false %}
      <!-- 购物车为空时的推荐 -->
      {% assign fallback_collection = collections[section.settings.fallback_collection] %}
      {% if fallback_collection.products_count > 0 %}
        <div class="recommendations-grid">
          {% for product in fallback_collection.products limit: product_limit %}
            <div class="recommendation-item">
              {% if product.featured_image %}
                <div class="recommendation-item__image">
                  <a href="{{ product.url }}">
                    <img src="{{ product.featured_image | image_url: width: 400, height: 400 }}"
                         alt="{{ product.title | escape }}"
                         width="400"
                         height="400"
                         loading="lazy">
                  </a>
                  {% if product.compare_at_price > product.price %}
                    <div class="recommendation-item__badge recommendation-item__badge--sale">Sale</div>
                  {% endif %}
                </div>
              {% endif %}
              
              <div class="recommendation-item__content">
                <h3 class="recommendation-item__title">
                  <a href="{{ product.url }}">{{ product.title }}</a>
                </h3>
                
                <div class="recommendation-item__price">
                  {% if product.compare_at_price > product.price %}
                    <span class="compare-price">{{ product.compare_at_price | money }}</span>
                  {% endif %}
                  {{ product.price | money }}
                </div>

                <div class="recommendation-item__actions">
                  <form action="{{ routes.cart_add_url }}" method="post" enctype="multipart/form-data" class="product-form">
                    <input type="hidden" name="id" value="{{ product.selected_or_first_available_variant.id }}">
                    <button type="submit" class="add-to-cart-btn" {% unless product.available %}disabled{% endunless %}>
                      <span class="btn-icon"></span>
                      {% if product.available %}
                        Add to Cart
                      {% else %}
                        Sold Out
                      {% endif %}
                    </button>
                  </form>
                </div>
              </div>
            </div>
          {% endfor %}
        </div>
      {% else %}
        <div class="empty-recommendations">
          <div class="empty-recommendations__icon">🛍️</div>
          <p class="empty-recommendations__text">{{ section.settings.empty_message | default: "Discover our amazing products" }}</p>
          <a href="{{ routes.all_products_collection_url }}" class="empty-recommendations__link">
            {{ section.settings.browse_text | default: "Browse All Products" }}
          </a>
        </div>
      {% endif %}
      
    {% elsif cart.item_count > 0 %}
      <!-- 基于购物车内容的智能推荐 -->
      <!-- 调试信息：显示购物车商品信息 -->
      {% comment %}
      <div style="background: #f0f0f0; padding: 10px; margin: 10px 0; font-size: 12px; border-radius: 4px;">
        <strong>Debug Info:</strong><br>
        Cart Items Count: {{ cart.item_count }}<br>
        {% for item in cart.items %}
          Cart Item {{ forloop.index }}: {{ item.product.title }}<br>
          - Type: "{{ item.product.product_type }}"<br>
          - Tags: {{ item.product.tags | join: ', ' }}<br>
          - Vendor: "{{ item.product.vendor }}"<br>
          - Collections: {% for collection in item.product.collections %}{{ collection.handle }}{% unless forloop.last %}, {% endunless %}{% endfor %}<br>
        {% endfor %}
        <br>
        Collected Cart Types: "{{ cart_product_types }}"<br>
        Collected Cart Tags: "{{ cart_product_tags }}"<br>
        Collected Cart Vendors: "{{ cart_product_vendors }}"<br>
        Collected Cart Collections: "{{ cart_product_collections }}"<br>
        Recommended Count: {{ recommended_count }}<br>
        Recommended Handles: {{ recommended_handles_array | join: ', ' }}
      </div>
      {% endcomment %}

      {% if recommended_handles_array.size > 0 %}
        <div class="recommendations-grid">
          {% for product_handle in recommended_handles_array limit: product_limit %}
            {% assign product = all_products[product_handle] %}
            {% if product %}
              <div class="recommendation-item">
                {% if product.featured_image %}
                  <div class="recommendation-item__image">
                    <a href="{{ product.url }}">
                      <img src="{{ product.featured_image | image_url: width: 400, height: 400 }}"
                           alt="{{ product.title | escape }}"
                           width="400"
                           height="400"
                           loading="lazy">
                    </a>
                    {% if product.compare_at_price > product.price %}
                      <div class="recommendation-item__badge recommendation-item__badge--sale">Sale</div>
                    {% endif %}
                  </div>
                {% endif %}

                <div class="recommendation-item__content">
                  <h3 class="recommendation-item__title">
                    <a href="{{ product.url }}">{{ product.title }}</a>
                  </h3>

                  <div class="recommendation-item__price">
                    {% if product.compare_at_price > product.price %}
                      <span class="compare-price">{{ product.compare_at_price | money }}</span>
                    {% endif %}
                    {{ product.price | money }}
                  </div>

                  <div class="recommendation-item__actions">
                    <form action="{{ routes.cart_add_url }}" method="post" enctype="multipart/form-data" class="product-form">
                      <input type="hidden" name="id" value="{{ product.selected_or_first_available_variant.id }}">
                      <button type="submit" class="add-to-cart-btn" {% unless product.available %}disabled{% endunless %}>
                        <span class="btn-icon"></span>
                        {% if product.available %}
                          Add to Cart
                        {% else %}
                          Sold Out
                        {% endif %}
                      </button>
                    </form>
                  </div>
                </div>
              </div>
            {% endif %}
          {% endfor %}
        </div>
      {% else %}
        <!-- 没有找到相关推荐时的后备方案 -->
        {% assign fallback_collection = collections[section.settings.fallback_collection] %}
        {% if fallback_collection.products_count > 0 %}
          <div class="recommendations-grid">
            {% for product in fallback_collection.products limit: product_limit %}
              {% assign skip_product = false %}
              {% for cart_item in cart.items %}
                {% if cart_item.product.id == product.id %}
                  {% assign skip_product = true %}
                  {% break %}
                {% endif %}
              {% endfor %}
              
              {% unless skip_product %}
                <div class="recommendation-item">
                  {% if product.featured_image %}
                    <div class="recommendation-item__image">
                      <a href="{{ product.url }}">
                        <img src="{{ product.featured_image | image_url: width: 400, height: 400 }}"
                             alt="{{ product.title | escape }}"
                             width="400"
                             height="400"
                             loading="lazy">
                      </a>
                      {% if product.compare_at_price > product.price %}
                        <div class="recommendation-item__badge recommendation-item__badge--sale">Sale</div>
                      {% endif %}
                    </div>
                  {% endif %}
                  
                  <div class="recommendation-item__content">
                    <h3 class="recommendation-item__title">
                      <a href="{{ product.url }}">{{ product.title }}</a>
                    </h3>
                    
                    <div class="recommendation-item__price">
                      {% if product.compare_at_price > product.price %}
                        <span class="compare-price">{{ product.compare_at_price | money }}</span>
                      {% endif %}
                      {{ product.price | money }}
                    </div>

                    <div class="recommendation-item__actions">
                      <form action="{{ routes.cart_add_url }}" method="post" enctype="multipart/form-data" class="product-form">
                        <input type="hidden" name="id" value="{{ product.selected_or_first_available_variant.id }}">
                        <button type="submit" class="add-to-cart-btn" {% unless product.available %}disabled{% endunless %}>
                          <span class="btn-icon"></span>
                          {% if product.available %}
                            Add to Cart
                          {% else %}
                            Sold Out
                          {% endif %}
                        </button>
                      </form>
                    </div>
                  </div>
                </div>
              {% endunless %}
            {% endfor %}
          </div>
        {% endif %}
      {% endif %}
      
    {% else %}
      <div class="empty-recommendations">
        <div class="empty-recommendations__icon">🛍️</div>
        <p class="empty-recommendations__text">{{ section.settings.empty_message | default: "Start shopping to see personalized recommendations" }}</p>
        <a href="{{ routes.all_products_collection_url }}" class="empty-recommendations__link">
          {{ section.settings.browse_text | default: "Browse All Products" }}
        </a>
      </div>
    {% endif %}
  </div>
</div>
{% endif %}

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // 为所有添加到购物车按钮添加事件监听器
    const addToCartForms = document.querySelectorAll('.cart-smart-recommendations .product-form');

    addToCartForms.forEach(form => {
      form.addEventListener('submit', function(e) {
        e.preventDefault();

        const button = form.querySelector('.add-to-cart-btn');
        const originalText = button.innerHTML;

        // 显示加载状态
        button.innerHTML = '<span class="btn-icon" style="animation: spin 1s linear infinite;">⟳</span> Adding...';
        button.disabled = true;

        // 使用原生表单提交到购物车
        const formData = new FormData(form);

        fetch('/cart/add', {
          method: 'POST',
          body: formData
        })
        .then(response => {
          if (response.ok) {
            // 成功添加到购物车
            button.innerHTML = '<span class="btn-icon">✓</span> Added!';
            button.style.background = '#28a745';

            // 延迟1.5秒后刷新页面以显示更新的购物车
            setTimeout(() => {
              window.location.reload();
            }, 1500);
          } else {
            throw new Error('Failed to add to cart');
          }
        })
        .catch(error => {
          console.error('Error adding to cart:', error);

          // 显示错误状态
          button.innerHTML = '<span class="btn-icon">!</span> Error';
          button.style.background = '#dc3545';

          // 2秒后恢复按钮状态
          setTimeout(() => {
            button.innerHTML = originalText;
            button.disabled = false;
            button.style.background = '';
          }, 2000);
        });
      });
    });
  });
</script>

{% schema %}
{
  "name": "Cart Recommendations",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Section Title",
      "default": "You May Also Like"
    },
    {
      "type": "text",
      "id": "subtitle",
      "label": "Section Subtitle",
      "info": "Optional subtitle text"
    },
    {
      "type": "collection",
      "id": "fallback_collection",
      "label": "Fallback Collection",
      "info": "Collection to use when cart is empty or no smart recommendations found"
    },
    {
      "type": "range",
      "id": "per_row",
      "label": "Products per row",
      "default": 4,
      "min": 2,
      "max": 5,
      "step": 1
    },
    {
      "type": "range",
      "id": "rows",
      "label": "Number of rows",
      "default": 1,
      "min": 1,
      "max": 3,
      "step": 1
    },
    {
      "type": "text",
      "id": "empty_message",
      "label": "Empty State Message",
      "default": "Start shopping to see personalized recommendations"
    },
    {
      "type": "text",
      "id": "browse_text",
      "label": "Browse Button Text",
      "default": "Browse All Products"
    }
  ]
}
{% endschema %}
