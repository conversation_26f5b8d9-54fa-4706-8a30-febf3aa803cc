<style>
  .modern-cart-item {
    display: grid;
    grid-template-columns: 120px 1fr auto auto auto;
    gap: 20px;
    align-items: center;
    padding: 25px 30px;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.3s ease;
    position: relative;
  }
  
  .modern-cart-item:hover {
    background-color: #fafbfc;
  }
  
  .modern-cart-item:last-child {
    border-bottom: none;
  }
  
  .cart-item-image {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    aspect-ratio: 1;
    background: #f8f9fa;
  }
  
  .cart-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }
  
  .cart-item-image:hover img {
    transform: scale(1.05);
  }
  
  .cart-item-details {
    min-width: 0;
  }
  
  .cart-item-title {
    font-size: 1.1em;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
    line-height: 1.3;
  }
  
  .cart-item-title a {
    color: inherit;
    text-decoration: none;
    transition: color 0.3s ease;
  }
  
  .cart-item-title a:hover {
    color: var(--colorAccent, #007bff);
  }
  
  .cart-item-variants {
    font-size: 0.9em;
    color: #6c757d;
    margin-bottom: 8px;
  }
  
  .cart-item-variants div {
    margin-bottom: 4px;
  }
  
  .cart-item-variants span {
    font-weight: 600;
    color: #495057;
  }
  
  .cart-item-properties {
    font-size: 0.85em;
    color: #6c757d;
    margin-bottom: 4px;
  }
  
  .cart-item-properties span {
    font-weight: 600;
    color: #495057;
  }
  
  .cart-item-selling-plan {
    font-size: 0.85em;
    color: #28a745;
    font-weight: 500;
    margin-top: 4px;
  }
  
  .cart-item-quantity {
    display: flex;
    align-items: center;
    background: white;
    border-radius: 12px;
    border: 2px solid #e9ecef;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  }

  .qty-btn {
    background: #f8f9fa;
    border: none;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #495057;
    transition: all 0.3s ease;
    font-size: 1.4em;
    font-weight: 700;
    position: relative;
  }

  .qty-btn:hover {
    background: var(--colorAccent, #007bff);
    color: white;
    transform: scale(1.05);
  }

  .qty-btn:active {
    transform: scale(0.95);
  }

  .qty-btn--minus {
    border-radius: 10px 0 0 10px;
  }

  .qty-btn--plus {
    border-radius: 0 10px 10px 0;
  }

  .qty-input {
    width: 60px;
    height: 40px;
    border: none;
    background: white;
    text-align: center;
    font-weight: 700;
    color: #2c3e50;
    font-size: 1em;
    border-left: 1px solid #e9ecef;
    border-right: 1px solid #e9ecef;
  }

  .qty-input:focus {
    outline: none;
    background: #fff;
    box-shadow: inset 0 0 0 2px var(--colorAccent, #007bff);
  }
  
  .cart-item-price {
    text-align: right;
    min-width: 100px;
  }
  
  .cart-item-price .original-price {
    font-size: 0.9em;
    color: #6c757d;
    text-decoration: line-through;
    display: block;
    margin-bottom: 4px;
  }
  
  .cart-item-price .current-price {
    font-size: 1.1em;
    font-weight: 700;
    color: #2c3e50;
  }
  
  .cart-item-price .sale-price {
    color: #e74c3c;
  }
  
  .cart-item-discounts {
    font-size: 0.85em;
    color: #28a745;
    margin-top: 4px;
  }
  
  .cart-item-unit-price {
    font-size: 0.8em;
    color: #6c757d;
    margin-top: 4px;
  }

  .cart-item-remove {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    color: #6c757d;
    cursor: pointer;
    font-size: 1em;
    padding: 0;
    border-radius: 12px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    position: relative;
  }

  .cart-item-remove:hover {
    background: #ff4757;
    border-color: #ff4757;
    color: white;
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(255, 71, 87, 0.3);
  }

  .cart-item-remove:active {
    transform: scale(0.95);
  }

  .cart-item-remove::before {
    content: '×';
    font-size: 1.8em;
    font-weight: 300;
    line-height: 1;
  }
  
  @media (max-width: 768px) {
    .modern-cart-item {
      grid-template-columns: 80px 1fr auto;
      grid-template-rows: auto auto auto;
      gap: 15px;
      padding: 20px 15px;
    }

    .cart-item-image {
      grid-row: 1 / 3;
    }

    .cart-item-details {
      grid-column: 2;
      grid-row: 1;
    }

    .cart-item-remove {
      grid-column: 3;
      grid-row: 1;
      align-self: start;
    }

    .cart-item-quantity {
      grid-column: 1 / 2;
      grid-row: 3;
      justify-self: start;
      margin-top: 10px;
    }

    .cart-item-price {
      grid-column: 2 / 4;
      grid-row: 3;
      text-align: right;
      align-self: end;
    }
    
    .cart-item-title {
      font-size: 1em;
    }
    
    .qty-btn {
      width: 32px;
      height: 32px;
    }
    
    .qty-input {
      width: 40px;
      height: 32px;
    }
  }
</style>

<div class="modern-cart-item" data-key="{{ product.key }}">
  <div class="cart-item-image" data-aos>
    {% if product.image != blank %}
      <a href="{{ product.url }}">
        {%- render 'image-element',
          img: product,
          alt: product.product.title,
          sizes: sizes,
          sizeVariable: sizeVariable,
          fallback: fallback,
          widths: '180, 360, 540',
        -%}
      </a>
    {% endif %}
  </div>

  <div class="cart-item-details">
    <h3 class="cart-item-title">
      <a href="{{ product.url }}">{{ product.product.title }}</a>
    </h3>

    {%- unless product.product.has_only_default_variant -%}
      <div class="cart-item-variants">
        {%- for option in product.options_with_values -%}
          <div><span>{{ option.name }}:</span> {{ option.value }}</div>
        {%- endfor -%}
      </div>
    {%- endunless -%}
    
    <div class="cart-item-selling-plan {% if product.selling_plan_allocation == empty %}hide {% endif %}">
      {{ product.selling_plan_allocation.selling_plan.name }}
    </div>

    {%- assign property_size = product.properties | size -%}
    {%- if property_size > 0 -%}
      {%- for p in product.properties -%}
        {%- assign first_character_in_key = p.first | truncate: 1, '' -%}
        {%- unless p.last == blank or first_character_in_key == '_' -%}
          <div class="cart-item-properties">
            <span>{{ p.first }}:</span>
            {% if p.last contains '/uploads/' %}
              <a href="{{ p.last }}">{{ p.last | split: '/' | last }}</a>
            {% else %}
              {{ p.last }}
            {% endif %}
          </div>
        {%- endunless -%}
      {%- endfor -%}
    {%- endif -%}
  </div>
  
  <div class="cart-item-quantity">
    <button type="button" class="qty-btn qty-btn--minus js-qty__adjust js-qty__adjust--minus" aria-label="{{ 'cart.general.reduce_quantity' | t }}">
      −
    </button>
    <input type="text"
      id="cart_updates_{{ product.key }}"
      name="updates[]"
      class="qty-input js-qty__num"
      value="{{ product.quantity }}"
      min="0"
      pattern="[0-9]*"
      data-id="{{ product.key }}">
    <button type="button" class="qty-btn qty-btn--plus js-qty__adjust js-qty__adjust--plus" aria-label="{{ 'cart.general.increase_quantity' | t }}">
      +
    </button>
  </div>
  
  <div class="cart-item-price">
    {%- if product.original_price != product.final_price -%}
      <span class="original-price">{{ product.original_price | money }}</span>
      <span class="current-price sale-price">{{ product.final_price | money }}</span>
    {%- else -%}
      <span class="current-price">{{ product.original_price | money }}</span>
    {%- endif -%}

    {%- if product.line_level_discount_allocations != blank -%}
      <div class="cart-item-discounts">
        {%- for discount_allocation in product.line_level_discount_allocations -%}
          <div>{{ discount_allocation.discount_application.title }} (-{{ discount_allocation.amount | money }})</div>
        {%- endfor -%}
      </div>
    {%- endif -%}

    {%- if product.unit_price_measurement -%}
      {%- capture unit_price_base_unit -%}
        <span class="unit-price-base">
          {%- if product.unit_price_measurement -%}
            {%- if product.unit_price_measurement.reference_value != 1 -%}
              {{ product.unit_price_measurement.reference_value }}
            {%- endif -%}
            {{ product.unit_price_measurement.reference_unit }}
          {%- endif -%}
        </span>
      {%- endcapture -%}

      <div class="cart-item-unit-price">{{ product.unit_price | money }}/{{ unit_price_base_unit }}</div>
    {%- endif -%}
  </div>

  <div class="cart-item-remove-wrapper">
    <button type="button"
            class="cart-item-remove js-cart-remove"
            data-key="{{ product.key }}"
            aria-label="{{ 'cart.general.remove' | t }}">
    </button>
  </div>
</div>
